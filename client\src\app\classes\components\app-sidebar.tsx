"use client";
import * as React from "react";
import { memo } from "react";
import {
  BadgeCheck,
  Briefcase,
  ClipboardList,
  FileText,
  GraduationCap,
  ImageIcon,
  UserCheck,
  CreditCard,
  Wallet,
  History,
} from "lucide-react";
import {
  <PERSON>bar,
  Sidebar<PERSON>ontent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import Image from "next/image";
import { NavMain } from "./nav-main";
import { NavUser } from "./nav-user";
import { MdClass } from "react-icons/md";

const defaultNavMain = [
  {
    title: "Profile",
    icon: ClipboardList,
    children: [
      { title: "Profile", url: "/profile", icon: UserCheck },
      { title: "Descriptions", url: "/profile/description", icon: FileText },
      { title: "Photo & Logo", url: "/profile/photo-and-logo", icon: ImageIcon },
      { title: "Education", url: "/profile/education", icon: GraduationCap },
      { title: "Experience", url: "/profile/experience", icon: Briefcase },
      { title: "Certificates", url: "/profile/certificates", icon: BadgeCheck },
      { title: "Tution Class", url: "/profile/tution-class", icon: MdClass },
    ],
  },
  {
    title: "Payment",
    icon: CreditCard,
    children: [
      { title: "Make Payment", url: "/payment", icon: Wallet },
      { title: "Payment History", url: "/payment/history", icon: History },
    ],
  },
];

const MemoizedNavMain = memo(NavMain);

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" variant="floating" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <div className="w-full flex justify-center">
              <SidebarMenuButton
                asChild
                className="data-[slot=sidebar-menu-button]:!p-1.5"
              >
                <a href="#">
                  <Image
                    src="/logo.png"
                    alt="App Logo"
                    width={120}
                    height={30}
                    className="rounded-md"
                  />
                </a>
              </SidebarMenuButton>
            </div>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <MemoizedNavMain items={defaultNavMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}