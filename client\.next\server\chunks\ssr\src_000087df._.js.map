{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn('col-start-2 row-span-2 row-start-1 self-start justify-self-end', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return <div data-slot=\"card-content\" className={cn('px-6', className)} {...props} />;\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardAction, CardDescription, CardContent };\r\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kEAAkE;QAC/E,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBAAO,8OAAC;QAAI,aAAU;QAAe,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;AAClF;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/app/classes/payment/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useState, useCallback } from 'react';\nimport { Badge } from '@/components/ui/badge';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { format } from 'date-fns';\nimport { axiosInstance } from '@/lib/axios';\nimport { useSelector } from 'react-redux';\nimport { RootState } from '@/store';\nimport Footer from '@/app-components/Footer';\nimport Header from '@/app-components/Header';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { ChevronDown, ChevronUp, Filter, RefreshCw, CreditCard, Wallet, History, Shield, Clock, CheckCircle } from 'lucide-react';\nimport { toast } from 'sonner';\nimport { Input } from '@/components/ui/input';\nimport { cn } from '@/lib/utils';\nimport { z } from \"zod\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\n\ninterface Transaction {\n  id: string;\n  type: 'CREDIT' | 'DEBIT';\n  amount: number;\n  reason: string;\n  createdAt: string;\n}\n\nconst formSchema = z.object({\n  amount: z\n    .string()\n    .min(1, \"Amount is required\")\n    .refine((val) => {\n      const num = parseInt(val, 10);\n      return !isNaN(num) && num >= 1 && num <= 50000;\n    }, {\n      message: \"Amount must be between ₹1 and ₹50,000\",\n    }),\n});\n\nconst PaymentPage = () => {\n  const [coins, setCoins] = useState<number>(0);\n  const [transactions, setTransactions] = useState<Transaction[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [filter, setFilter] = useState<'all' | 'credit' | 'debit'>('all');\n  const [sortByDate, setSortByDate] = useState<'asc' | 'desc'>('desc');\n  const { user }: any = useSelector((state: RootState) => state.user);\n  const [isPaying, setIsPaying] = useState(false);\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isSubmitting },\n    reset,\n  } = useForm({\n    resolver: zodResolver(formSchema),\n  });\n\n  const onSubmit = async (values: { amount: string }) => {\n    await initiatePayment(parseInt(values.amount));\n  };\n\n  const initiatePayment = async (amt: number) => {\n    setIsPaying(true);\n    try {\n      const res = await axiosInstance.post('/coins/create-order/class', {\n        amount: amt * 100,\n      });\n\n      const { order } = res.data;\n\n      const options = {\n        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,\n        amount: order.amount,\n        currency: 'INR',\n        name: 'Class Payment - Uest',\n        description: 'Payment for Class Services',\n        order_id: order.id,\n        prefill: {\n          name: user?.firstName + ' ' + user?.lastName,\n          email: user?.email,\n        },\n        handler: async function (response: any) {\n          try {\n            await axiosInstance.post('/coins/verify/class', {\n              razorpay_order_id: response.razorpay_order_id,\n              razorpay_payment_id: response.razorpay_payment_id,\n              razorpay_signature: response.razorpay_signature,\n              amount: amt * 100,\n            });\n\n            toast.success('Payment completed successfully!');\n            fetchData();\n            reset();\n          } catch {\n            toast.error('Payment verification failed');\n          }\n        },\n        theme: {\n          color: '#f97316',\n        },\n      };\n\n      const rzp = new (window as any).Razorpay(options);\n      rzp.open();\n    } catch {\n      toast.error('Payment initialization failed');\n    } finally {\n      setIsPaying(false);\n    }\n  };\n\n  useEffect(() => {\n    const script = document.createElement('script');\n    script.src = 'https://checkout.razorpay.com/v1/checkout.js';\n    script.async = true;\n    document.body.appendChild(script);\n  }, []);\n\n  const fetchData = useCallback(async () => {\n    setIsLoading(true);\n    try {\n      const coinsResponse = await axiosInstance.get('/coins/get-total-coins');\n      const transactionsResponse = await axiosInstance.get('/coins/transaction-history');\n\n      setCoins(coinsResponse.data.coins);\n      setTransactions(transactionsResponse.data.transactions);\n    } catch (error) {\n      toast.error('Failed to load payment data. Please try again.');\n      console.error('Error fetching data', error);\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  useEffect(() => {\n    fetchData();\n  }, [user?.id, fetchData]);\n\n  const filteredTransactions = transactions\n    .filter((txn) => filter === 'all' || txn.type.toLowerCase() === filter)\n    .sort((a, b) => {\n      const dateA = new Date(a.createdAt).getTime();\n      const dateB = new Date(b.createdAt).getTime();\n      return sortByDate === 'desc' ? dateB - dateA : dateA - dateB;\n    });\n\n  const TransactionCard = ({ txn }: { txn: Transaction }) => {\n    const [isExpanded, setIsExpanded] = useState(false);\n\n    return (\n      <Card\n        className=\"p-4 bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200 cursor-pointer\"\n        onClick={() => setIsExpanded(!isExpanded)}\n      >\n        <div className=\"flex items-center justify-between\">\n          <div className=\"space-y-1\">\n            <h3 className=\"text-base font-semibold text-foreground\">\n              {txn.type === 'CREDIT' ? (\n                <span className=\"text-green-500 flex items-center gap-2\">\n                  <Wallet className=\"h-4 w-4\" />\n                  Credit\n                </span>\n              ) : (\n                <span className=\"text-red-500 flex items-center gap-2\">\n                  <CreditCard className=\"h-4 w-4\" />\n                  Debit\n                </span>\n              )}\n            </h3>\n            <p className=\"text-sm text-muted-foreground\">\n              ₹{txn.amount} • {format(new Date(txn.createdAt), 'MMM dd, yyyy')}\n            </p>\n          </div>\n          {isExpanded ? (\n            <ChevronUp className=\"h-5 w-5 text-muted-foreground\" />\n          ) : (\n            <ChevronDown className=\"h-5 w-5 text-muted-foreground\" />\n          )}\n        </div>\n        {isExpanded && (\n          <div className=\"mt-3 pt-3 border-t text-sm text-muted-foreground space-y-1 animate-in fade-in\">\n            <p>\n              <strong>Reason:</strong> {txn.reason}\n            </p>\n            <p>\n              <strong>Time:</strong> {format(new Date(txn.createdAt), 'p')}\n            </p>\n          </div>\n        )}\n      </Card>\n    );\n  };"], "names": [], "mappings": ";;AAEA;AAGA;AAEA;AACA;AACA;AAKA;AAAA;AAAA;AAAA;AACA;AAGA;AACA;AACA;AApBA;;;;;;;;;;;;AA8BA,MAAM,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1B,QAAQ,oIAAA,CAAA,IAAC,CACN,MAAM,GACN,GAAG,CAAC,GAAG,sBACP,MAAM,CAAC,CAAC;QACP,MAAM,MAAM,SAAS,KAAK;QAC1B,OAAO,CAAC,MAAM,QAAQ,OAAO,KAAK,OAAO;IAC3C,GAAG;QACD,SAAS;IACX;AACJ;AAEA,MAAM,cAAc;IAClB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8B;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAC7D,MAAM,EAAE,IAAI,EAAE,GAAQ,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,CAAC,QAAqB,MAAM,IAAI;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACnC,KAAK,EACN,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE;QACV,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,MAAM,gBAAgB,SAAS,OAAO,MAAM;IAC9C;IAEA,MAAM,kBAAkB,OAAO;QAC7B,YAAY;QACZ,IAAI;YACF,MAAM,MAAM,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,6BAA6B;gBAChE,QAAQ,MAAM;YAChB;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,IAAI,IAAI;YAE1B,MAAM,UAAU;gBACd,GAAG;gBACH,QAAQ,MAAM,MAAM;gBACpB,UAAU;gBACV,MAAM;gBACN,aAAa;gBACb,UAAU,MAAM,EAAE;gBAClB,SAAS;oBACP,MAAM,MAAM,YAAY,MAAM,MAAM;oBACpC,OAAO,MAAM;gBACf;gBACA,SAAS,eAAgB,QAAa;oBACpC,IAAI;wBACF,MAAM,mHAAA,CAAA,gBAAa,CAAC,IAAI,CAAC,uBAAuB;4BAC9C,mBAAmB,SAAS,iBAAiB;4BAC7C,qBAAqB,SAAS,mBAAmB;4BACjD,oBAAoB,SAAS,kBAAkB;4BAC/C,QAAQ,MAAM;wBAChB;wBAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;wBACd;wBACA;oBACF,EAAE,OAAM;wBACN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd;gBACF;gBACA,OAAO;oBACL,OAAO;gBACT;YACF;YAEA,MAAM,MAAM,IAAI,AAAC,OAAe,QAAQ,CAAC;YACzC,IAAI,IAAI;QACV,EAAE,OAAM;YACN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,YAAY;QACd;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,SAAS,aAAa,CAAC;QACtC,OAAO,GAAG,GAAG;QACb,OAAO,KAAK,GAAG;QACf,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,aAAa;QACb,IAAI;YACF,MAAM,gBAAgB,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;YAC9C,MAAM,uBAAuB,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC;YAErD,SAAS,cAAc,IAAI,CAAC,KAAK;YACjC,gBAAgB,qBAAqB,IAAI,CAAC,YAAY;QACxD,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,uBAAuB;QACvC,SAAU;YACR,aAAa;QACf;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC,MAAM;QAAI;KAAU;IAExB,MAAM,uBAAuB,aAC1B,MAAM,CAAC,CAAC,MAAQ,WAAW,SAAS,IAAI,IAAI,CAAC,WAAW,OAAO,QAC/D,IAAI,CAAC,CAAC,GAAG;QACR,MAAM,QAAQ,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;QAC3C,MAAM,QAAQ,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;QAC3C,OAAO,eAAe,SAAS,QAAQ,QAAQ,QAAQ;IACzD;IAEF,MAAM,kBAAkB,CAAC,EAAE,GAAG,EAAwB;QACpD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAE7C,qBACE,8OAAC,gIAAA,CAAA,OAAI;YACH,WAAU;YACV,SAAS,IAAM,cAAc,CAAC;;8BAE9B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,IAAI,IAAI,KAAK,yBACZ,8OAAC;wCAAK,WAAU;;0DACd,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;6DAIhC,8OAAC;wCAAK,WAAU;;0DACd,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;8CAKxC,8OAAC;oCAAE,WAAU;;wCAAgC;wCACzC,IAAI,MAAM;wCAAC;wCAAI,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,IAAI,SAAS,GAAG;;;;;;;;;;;;;wBAGpD,2BACC,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;iDAErB,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;;gBAG1B,4BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;8CAAO;;;;;;gCAAgB;gCAAE,IAAI,MAAM;;;;;;;sCAEtC,8OAAC;;8CACC,8OAAC;8CAAO;;;;;;gCAAc;gCAAE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,IAAI,SAAS,GAAG;;;;;;;;;;;;;;;;;;;IAMpE;AAAC", "debugId": null}}]}