'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { format } from 'date-fns';
import { axiosInstance } from '@/lib/axios';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import Header from '@/app-components/Header';
import Footer from '@/app-components/Footer';
import Image from 'next/image';
import { Skeleton } from '@/components/ui/skeleton';
import { ChevronDown, ChevronUp, Filter, RefreshCw, CreditCard, Wallet } from 'lucide-react';
import { toast } from 'sonner';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

interface Transaction {
  id: string;
  type: 'CREDIT' | 'DEBIT';
  amount: number;
  reason: string;
  createdAt: string;
}

const formSchema = z.object({
  amount: z
    .string()
    .min(1, "Amount is required")
    .refine((val) => {
      const num = parseInt(val, 10);
      return !isNaN(num) && num >= 1 && num <= 50000;
    }, {
      message: "Amount must be between ₹1 and ₹50,000",
    }),
});

const ClassesPaymentPage = () => {
  const [coins, setCoins] = useState<number>(0);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'credit' | 'debit'>('all');
  const [sortByDate, setSortByDate] = useState<'asc' | 'desc'>('desc');
  const { user }: any = useSelector((state: RootState) => state.user);

  const [isPaying, setIsPaying] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm({
    resolver: zodResolver(formSchema),
  });

  const onSubmit = async (values: { amount: string }) => {
    await initiatePayment(parseInt(values.amount));
  };

  const initiatePayment = async (amt: number) => {
    setIsPaying(true);
    try {
      const res = await axiosInstance.post('/coins/create-order/class', {
        amount: amt * 100,
      });

      const { order } = res.data;

      const options = {
        key: process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID,
        amount: order.amount,
        currency: 'INR',
        name: 'Class Payment - Uest',
        description: 'Payment for Class Services',
        order_id: order.id,
        prefill: {
          name: user?.firstName + ' ' + user?.lastName,
          email: user?.email,
        },
        handler: async function (response: any) {
          try {
            await axiosInstance.post('/coins/verify/class', {
              razorpay_order_id: response.razorpay_order_id,
              razorpay_payment_id: response.razorpay_payment_id,
              razorpay_signature: response.razorpay_signature,
              amount: amt * 100,
            });

            toast.success('Payment completed successfully!');
            fetchData();
            reset();
          } catch {
            toast.error('Payment verification failed');
          }
        },
        theme: {
          color: '#f97316',
        },
      };

      const rzp = new (window as any).Razorpay(options);
      rzp.open();
    } catch {
      toast.error('Payment initialization failed');
    } finally {
      setIsPaying(false);
    }
  };

  useEffect(() => {
    const script = document.createElement('script');
    script.src = 'https://checkout.razorpay.com/v1/checkout.js';
    script.async = true;
    document.body.appendChild(script);
  }, []);

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    try {
      const coinsResponse = await axiosInstance.get('/coins/get-total-coins');
      const transactionsResponse = await axiosInstance.get('/coins/transaction-history');

      setCoins(coinsResponse.data.coins);
      setTransactions(transactionsResponse.data.transactions);
    } catch (error) {
      toast.error('Failed to load payment data. Please try again.');
      console.error('Error fetching data', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchData();
  }, [user?.id, fetchData]);

  const filteredTransactions = transactions
    .filter((txn) => filter === 'all' || txn.type.toLowerCase() === filter)
    .sort((a, b) => {
      const dateA = new Date(a.createdAt).getTime();
      const dateB = new Date(b.createdAt).getTime();
      return sortByDate === 'desc' ? dateB - dateA : dateA - dateB;
    });

  const TransactionCard = ({ txn }: { txn: Transaction }) => {
    const [isExpanded, setIsExpanded] = useState(false);

    return (
      <Card
        className="p-4 bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <h3 className="text-base font-semibold text-foreground">
              {txn.type === 'CREDIT' ? (
                <span className="text-green-500 flex items-center gap-2">
                  <Wallet className="h-4 w-4" />
                  Credit
                </span>
              ) : (
                <span className="text-red-500 flex items-center gap-2">
                  <CreditCard className="h-4 w-4" />
                  Debit
                </span>
              )}
            </h3>
            <p className="text-sm text-muted-foreground">
              ₹{txn.amount} • {format(new Date(txn.createdAt), 'MMM dd, yyyy')}
            </p>
          </div>
          {isExpanded ? (
            <ChevronUp className="h-5 w-5 text-muted-foreground" />
          ) : (
            <ChevronDown className="h-5 w-5 text-muted-foreground" />
          )}
        </div>
        {isExpanded && (
          <div className="mt-3 pt-3 border-t text-sm text-muted-foreground space-y-1 animate-in fade-in">
            <p>
              <strong>Reason:</strong> {txn.reason}
            </p>
            <p>
              <strong>Time:</strong> {format(new Date(txn.createdAt), 'p')}
            </p>
          </div>
        )}
      </Card>
    );
  };

  return (
    <>
      <Header />
      <div className="px-4 sm:px-6 lg:px-8 py-12 max-w-7xl mx-auto space-y-6">
        <div className="sticky top-16 z-10 bg-background/95 backdrop-blur-sm supports-[backdrop-filter]:bg-background/60 py-4">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div className="flex items-center space-x-4">
              <div className="relative w-12 h-12 rounded-full bg-orange-100 p-2">
                <CreditCard className="w-8 h-8 text-orange-600" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-foreground">
                  Class Payment Portal
                </h1>
                <Badge
                  variant="outline"
                  className="text-customOrange text-xl font-semibold border-customOrange mt-1"
                >
                  Balance: {coins} Coins
                </Badge>
              </div>
            </div>
            <Button
              variant="outline"
              onClick={fetchData}
              disabled={isLoading}
              className="flex gap-2 w-full sm:w-auto"
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)}>
          <Card className="p-6 bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200">
            <div className="flex items-center gap-3 mb-4">
              <Wallet className="h-6 w-6 text-orange-600" />
              <h2 className="text-lg font-semibold text-foreground">Make Payment</h2>
            </div>
            <div className="grid gap-4 sm:grid-cols-2">
              <div className="space-y-4">
                <div className="relative">
                  <Input
                    type="number"
                    placeholder="Enter amount (₹1 - ₹50,000)"
                    className="border-customOrange/30 focus:border-customOrange pr-10"
                    disabled={isPaying || isSubmitting}
                    {...register("amount")}
                  />
                  <span className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground text-sm">
                    ₹
                  </span>
                  {errors.amount && (
                    <p className="text-sm text-red-500 mt-1">{errors.amount.message}</p>
                  )}
                </div>

                <Button
                  type="submit"
                  disabled={isPaying || isSubmitting}
                  className={cn(
                    "w-full bg-customOrange hover:bg-orange-600 text-white",
                    (isPaying || isSubmitting) && "opacity-75 cursor-not-allowed"
                  )}
                >
                  {(isPaying || isSubmitting) && (
                    <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                  )}
                  {isPaying || isSubmitting ? "Processing Payment..." : "Pay Now"}
                </Button>
              </div>
              <div className="bg-orange-50 p-4 rounded-lg">
                <h3 className="font-medium text-orange-800 mb-2">Payment Information</h3>
                <ul className="text-sm text-orange-700 space-y-1">
                  <li>• Secure payment gateway</li>
                  <li>• Instant payment confirmation</li>
                  <li>• 24/7 customer support</li>
                  <li>• Multiple payment options</li>
                </ul>
              </div>
            </div>
          </Card>
        </form>

        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-20 w-full rounded-lg" />
            {[...Array(4)].map((_, i) => (
              <Skeleton key={i} className="h-16 w-full rounded-lg" />
            ))}
          </div>
        ) : (
          <div className="space-y-6">
            <div className="flex flex-col sm:flex-row gap-4 items-center">
              <div className="flex gap-2 flex-wrap">
                <Button
                  variant={filter === 'all' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilter("all")}
                  className={filter === "all" ? "bg-customOrange hover:bg-orange-600" : ""}
                >
                  All Transactions
                </Button>
                <Button
                  variant={filter === 'credit' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilter("credit")}
                  className={filter === "credit" ? "bg-customOrange hover:bg-orange-600" : ""}
                >
                  Credits
                </Button>
                <Button
                  variant={filter === 'debit' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setFilter("debit")}
                  className={filter === "debit" ? "bg-customOrange hover:bg-orange-600" : ""}
                >
                  Debits
                </Button>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortByDate(sortByDate === 'desc' ? 'asc' : 'desc')}
                className="flex gap-2"
              >
                <Filter className="h-4 w-4" />
                Sort by Date {sortByDate === 'desc' ? '↓' : '↑'}
              </Button>
            </div>

            {filteredTransactions.length > 0 ? (
              <div className="grid gap-4">
                {filteredTransactions.map((txn) => (
                  <TransactionCard key={txn.id} txn={txn} />
                ))}
              </div>
            ) : (
              <Card className="p-8 bg-white rounded-lg shadow-sm border text-center">
                <div className="relative w-24 h-24 mx-auto mb-4 opacity-50">
                  <CreditCard className="w-24 h-24 text-gray-400" />
                </div>
                <p className="text-muted-foreground mb-4">
                  No payment transactions found. Make your first payment to get started!
                </p>
                <Button
                  variant="outline"
                  className="bg-orange-50 text-customOrange hover:bg-orange-100"
                  onClick={fetchData}
                >
                  Refresh Data
                </Button>
              </Card>
            )}
          </div>
        )}
      </div>
      <Footer />
    </>
  );
};

export default ClassesPaymentPage;
