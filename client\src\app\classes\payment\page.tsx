'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Footer from '@/app-components/Footer';
import Header from '@/app-components/Header';
import { CreditCard, Wallet, History, Shield, Clock, CheckCircle } from 'lucide-react';
import { Input } from '@/components/ui/input';

const PaymentPage = () => {
  const [amount, setAmount] = useState('');

  return (
    <>
      <Header />
      <div className="container mx-auto px-4 py-8 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Payment Portal</h1>
            <p className="text-muted-foreground">
              Manage your payments and view transaction history
            </p>
          </div>
        </div>

        <Tabs defaultValue="payment" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="payment" className="flex items-center gap-2">
              <Wallet className="h-4 w-4" />
              Payment
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <History className="h-4 w-4" />
              Payment History
            </TabsTrigger>
          </TabsList>

          <TabsContent value="payment" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Wallet className="h-5 w-5 text-orange-600" />
                    Payment Form
                  </CardTitle>
                  <CardDescription>
                    Enter the amount you want to pay for class services
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <label htmlFor="amount" className="text-sm font-medium">
                        Amount (₹)
                      </label>
                      <div className="relative">
                        <Input
                          id="amount"
                          type="number"
                          placeholder="Enter amount (₹1 - ₹50,000)"
                          className="border-customOrange/30 focus:border-customOrange pr-10"
                          value={amount}
                          onChange={(e) => setAmount(e.target.value)}
                        />
                        <span className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground text-sm">
                          ₹
                        </span>
                      </div>
                    </div>

                    <Button className="w-full bg-customOrange hover:bg-orange-600 text-white">
                      Pay Now
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="h-5 w-5 text-green-600" />
                    Payment Information
                  </CardTitle>
                  <CardDescription>
                    Why choose our payment system?
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start gap-3">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium">Secure & Safe</h4>
                      <p className="text-sm text-muted-foreground">
                        256-bit SSL encryption protects your payment data
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Clock className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium">Instant Processing</h4>
                      <p className="text-sm text-muted-foreground">
                        Payments are processed immediately with instant confirmation
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <CreditCard className="h-5 w-5 text-purple-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium">Multiple Options</h4>
                      <p className="text-sm text-muted-foreground">
                        Support for cards, UPI, net banking, and wallets
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Wallet className="h-5 w-5 text-orange-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium">24/7 Support</h4>
                      <p className="text-sm text-muted-foreground">
                        Round-the-clock customer support for any payment issues
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <div className="space-y-6">
              <div className="flex flex-col sm:flex-row gap-4 items-center">
                <div className="flex gap-2 flex-wrap">
                  <Button variant="default" size="sm" className="bg-customOrange hover:bg-orange-600">
                    All Transactions
                  </Button>
                  <Button variant="outline" size="sm">
                    Credits
                  </Button>
                  <Button variant="outline" size="sm">
                    Debits
                  </Button>
                </div>
                <Button variant="outline" size="sm" className="flex gap-2">
                  <History className="h-4 w-4" />
                  Sort by Date ↓
                </Button>
              </div>

              <Card className="p-8 bg-white rounded-lg shadow-sm border text-center">
                <div className="relative w-24 h-24 mx-auto mb-4 opacity-50">
                  <CreditCard className="w-24 h-24 text-gray-400" />
                </div>
                <p className="text-muted-foreground mb-4">
                  No payment transactions found. Make your first payment to get started!
                </p>
                <Button variant="outline" className="bg-orange-50 text-customOrange hover:bg-orange-100">
                  Refresh Data
                </Button>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
      <Footer />
    </>
  );
};

export default PaymentPage;